2025-09-29 01:00:58 - utils.image_utils - INFO - Found 5 images in input
2025-09-29 01:01:13 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:01:14 - utils.image_utils - INFO - Found 5 images in input
2025-09-29 01:01:15 - api_clients.gemini_client - INFO - Initialized Gemini client with model: gemini-2.5-flash-preview-09-2025
2025-09-29 01:01:15 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:01:15 - processor - INFO - Starting batch processing of folder: ./input [OVERRIDE: Using auto_4K]
2025-09-29 01:01:15 - utils.image_utils - INFO - Found 5 images in input
2025-09-29 01:01:15 - processor - INFO - Found 5 images to process
2025-09-29 01:01:15 - processor - INFO - Processing image 1/5: zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:01:15 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:01:15 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:01:19 - processor - INFO - Reference hair color: black
2025-09-29 01:01:19 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:01:25 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:01:25 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:01:25 - processor - INFO - Using 2 reference image(s)
2025-09-29 01:01:25 - processor - INFO - Reference images within limit at 0.38 MB
2025-09-29 01:01:25 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:01:25 - api_clients.fal_client - INFO - Using 2 pre-encoded reference images
2025-09-29 01:01:25 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:01:25 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:01:26 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:01:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:01:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:02:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/b2e7a09b-6533-4765-8e73-53b773c834b1 "HTTP/1.1 200 OK"
2025-09-29 01:02:01 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:02:04 - utils.image_utils - INFO - Downloaded image to: output/zoeyiso_1755122525_3698558269334894120_47557972205_seedream_20250929_010202_1.jpg
2025-09-29 01:02:04 - processor - INFO - Successfully generated 1 images for zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:02:04 - utils.logger - INFO - Processing images: 1/5 (20.0%) - ETA: 197s - Completed: zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:02:04 - processor - INFO - Processing image 2/5: zoeyiso_1755122525_3698558269351614503_47557972205.jpg
2025-09-29 01:02:05 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:02:05 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:02:09 - processor - INFO - Reference hair color: black
2025-09-29 01:02:09 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755122525_3698558269351614503_47557972205.jpg
2025-09-29 01:02:14 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755122525_3698558269351614503_47557972205.jpg
2025-09-29 01:02:14 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:02:14 - processor - INFO - Using 2 reference image(s)
2025-09-29 01:02:14 - processor - INFO - Reference images within limit at 0.38 MB
2025-09-29 01:02:14 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:02:14 - api_clients.fal_client - INFO - Using 2 pre-encoded reference images
2025-09-29 01:02:14 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:02:14 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:02:15 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:02:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:02:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:03:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/31e54e10-8923-442b-a7ad-6da24384e1e8 "HTTP/1.1 200 OK"
2025-09-29 01:03:32 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:03:34 - utils.image_utils - INFO - Downloaded image to: output/zoeyiso_1755122525_3698558269351614503_47557972205_seedream_20250929_010332_1.jpg
2025-09-29 01:03:34 - processor - INFO - Successfully generated 1 images for zoeyiso_1755122525_3698558269351614503_47557972205.jpg
2025-09-29 01:03:34 - utils.logger - INFO - Processing images: 2/5 (40.0%) - ETA: 208s - Completed: zoeyiso_1755122525_3698558269351614503_47557972205.jpg
2025-09-29 01:03:34 - processor - INFO - Processing image 3/5: zoeyiso_1755122525_3698558269469195689_47557972205.jpg
2025-09-29 01:03:34 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:03:34 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:03:38 - processor - INFO - Reference hair color: black
2025-09-29 01:03:38 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755122525_3698558269469195689_47557972205.jpg
2025-09-29 01:03:44 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755122525_3698558269469195689_47557972205.jpg
2025-09-29 01:03:44 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:03:44 - processor - INFO - Using 2 reference image(s)
2025-09-29 01:03:44 - processor - INFO - Reference images within limit at 0.38 MB
2025-09-29 01:03:44 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:03:44 - api_clients.fal_client - INFO - Using 2 pre-encoded reference images
2025-09-29 01:03:44 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:03:44 - api_clients.fal_client - INFO - Total encoded images size: 1.23 MB
2025-09-29 01:03:45 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:03:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:03:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:04:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/2d4d447f-bed8-4d7f-9187-cf45f110d7e8 "HTTP/1.1 200 OK"
2025-09-29 01:04:25 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:04:26 - utils.image_utils - INFO - Downloaded image to: output/zoeyiso_1755122525_3698558269469195689_47557972205_seedream_20250929_010425_1.jpg
2025-09-29 01:04:26 - processor - INFO - Successfully generated 1 images for zoeyiso_1755122525_3698558269469195689_47557972205.jpg
2025-09-29 01:04:26 - utils.logger - INFO - Processing images: 3/5 (60.0%) - ETA: 127s - Completed: zoeyiso_1755122525_3698558269469195689_47557972205.jpg
2025-09-29 01:04:26 - processor - INFO - Processing image 4/5: zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:04:26 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:04:26 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:04:30 - processor - INFO - Reference hair color: dark brown
2025-09-29 01:04:30 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:04:34 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:04:34 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:04:34 - processor - INFO - Using 2 reference image(s)
2025-09-29 01:04:34 - processor - INFO - Reference images within limit at 0.38 MB
2025-09-29 01:04:34 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:04:34 - api_clients.fal_client - INFO - Using 2 pre-encoded reference images
2025-09-29 01:04:34 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:04:34 - api_clients.fal_client - INFO - Total encoded images size: 1.41 MB
2025-09-29 01:04:36 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:04:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:55 - processor - INFO - Cancellation requested
2025-09-29 01:04:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:04:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:05:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:05:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/106f8999-272e-41d3-ae40-98027a7b7113 "HTTP/1.1 200 OK"
2025-09-29 01:05:13 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:05:15 - utils.image_utils - INFO - Downloaded image to: output/zoeyiso_1755122525_3698558269653572264_47557972205_seedream_20250929_010513_1.jpg
2025-09-29 01:05:15 - processor - INFO - Successfully generated 1 images for zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:05:15 - utils.logger - INFO - Processing images: 4/5 (80.0%) - ETA: 60s - Completed: zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:05:15 - processor - INFO - Processing cancelled after image zoeyiso_1755122525_3698558269653572264_47557972205.jpg
2025-09-29 01:05:15 - utils.logger - INFO - Processing images: Processing cancelled - Time taken: 240.3s
2025-09-29 01:05:15 - processor - INFO - Processing report saved to: output/processing_report_20250929_010515.json
