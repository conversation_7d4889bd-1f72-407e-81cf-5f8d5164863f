2025-09-29 01:13:43 - utils.image_utils - INFO - Found 2 images in input
2025-09-29 01:15:26 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:15:27 - utils.image_utils - INFO - Found 2 images in input
2025-09-29 01:15:32 - api_clients.gemini_client - INFO - Initialized Gemini client with model: gemini-2.5-flash
2025-09-29 01:15:32 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:15:32 - processor - INFO - Starting batch processing of folder: ./input [OVERRIDE: Using auto_4K]
2025-09-29 01:15:32 - utils.image_utils - INFO - Found 2 images in input
2025-09-29 01:15:32 - processor - INFO - Found 2 images to process
2025-09-29 01:15:32 - processor - INFO - Processing image 1/2: zoeyiso_1755701824_3703417783127951197_47557972205.jpg
2025-09-29 01:15:32 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:15:32 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:15:37 - processor - INFO - Reference hair color: black
2025-09-29 01:15:37 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755701824_3703417783127951197_47557972205.jpg
2025-09-29 01:15:44 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755701824_3703417783127951197_47557972205.jpg
2025-09-29 01:15:44 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:15:44 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:15:45 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:15:45 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:15:45 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:15:45 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:15:45 - api_clients.fal_client - INFO - Total encoded images size: 1.81 MB
2025-09-29 01:15:46 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:15:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:15:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:16:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/39a6cb72-21c5-4727-ac3a-a79bf9d7d0fe "HTTP/1.1 200 OK"
2025-09-29 01:16:17 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:16:19 - utils.image_utils - INFO - Downloaded image to: output/zoeyiso_1755701824_3703417783127951197_47557972205_seedream_20250929_011617_1.jpg
2025-09-29 01:16:19 - processor - INFO - Successfully generated 1 images for zoeyiso_1755701824_3703417783127951197_47557972205.jpg
2025-09-29 01:16:19 - utils.logger - INFO - Processing images: 1/2 (50.0%) - ETA: 46s - Completed: zoeyiso_1755701824_3703417783127951197_47557972205.jpg
2025-09-29 01:16:19 - processor - INFO - Processing image 2/2: zoeyiso_1757541539_3718850428256998249_47557972205.jpg
2025-09-29 01:16:19 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:16:19 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:16:22 - processor - INFO - Reference hair color: black
2025-09-29 01:16:22 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1757541539_3718850428256998249_47557972205.jpg
2025-09-29 01:16:44 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1757541539_3718850428256998249_47557972205.jpg
2025-09-29 01:16:44 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:16:44 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:16:45 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:16:45 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:16:45 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:16:45 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:16:45 - api_clients.fal_client - INFO - Total encoded images size: 1.62 MB
2025-09-29 01:16:47 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:16:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0ddbe445-b94d-4699-a10a-43f52e91773e/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0ddbe445-b94d-4699-a10a-43f52e91773e/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0ddbe445-b94d-4699-a10a-43f52e91773e/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:16:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0ddbe445-b94d-4699-a10a-43f52e91773e/status?logs=false "HTTP/1.1 202 Accepted"
