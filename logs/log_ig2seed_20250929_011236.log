2025-09-29 01:12:36 - utils.image_utils - INFO - Found 6 images in input
2025-09-29 01:12:42 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:12:43 - utils.image_utils - INFO - Found 6 images in input
2025-09-29 01:12:45 - api_clients.gemini_client - INFO - Initialized Gemini client with model: gemini-2.5-flash
2025-09-29 01:12:45 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:12:45 - processor - INFO - Starting batch processing of folder: ./input [OVERRIDE: Using auto_4K]
2025-09-29 01:12:45 - utils.image_utils - INFO - Found 6 images in input
2025-09-29 01:12:45 - processor - INFO - Found 6 images to process
2025-09-29 01:12:45 - processor - INFO - Processing image 1/6: zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:12:45 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:12:45 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:12:50 - processor - INFO - Reference hair color: black
2025-09-29 01:12:50 - api_clients.gemini_client - INFO - Analyzing image: input/zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:12:58 - api_clients.gemini_client - INFO - Successfully analyzed image: zoeyiso_1755122525_3698558269334894120_47557972205.jpg
2025-09-29 01:12:58 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:12:58 - processor - INFO - Using 2 reference image(s)
2025-09-29 01:12:58 - processor - INFO - Reference images within limit at 0.38 MB
2025-09-29 01:12:58 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:12:58 - api_clients.fal_client - INFO - Using 2 pre-encoded reference images
2025-09-29 01:12:58 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:12:58 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:12:59 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:13:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:07 - processor - INFO - Cancellation requested
2025-09-29 01:13:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:13:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/a9f4d736-1555-44f0-a2e1-91c9ded74acb/status?logs=false "HTTP/1.1 202 Accepted"
