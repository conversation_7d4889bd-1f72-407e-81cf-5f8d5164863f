2025-09-29 01:53:54 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:53:59 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:54:00 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:54:21 - api_clients.gemini_client - INFO - Initialized Gemini client with model: gemini-2.5-flash
2025-09-29 01:54:21 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:54:21 - processor - INFO - Starting batch processing of folder: ./input [OVERRIDE: Using auto_4K]
2025-09-29 01:54:21 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:54:21 - processor - INFO - Found 30 images to process
2025-09-29 01:54:21 - processor - INFO - Processing image 1/30: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:21 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:54:21 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:54:25 - processor - INFO - Reference hair color: black
2025-09-29 01:54:25 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:28 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:28 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:54:28 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:54:29 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Total encoded images size: 1.08 MB
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3 "HTTP/1.1 200 OK"
2025-09-29 01:55:01 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:55:03 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978386344976_64462038780_seedream_20250929_015501_1.jpg
2025-09-29 01:55:03 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:55:03 - utils.logger - INFO - Processing images: 1/30 (3.3%) - ETA: 1236s - Completed: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:55:03 - processor - INFO - Processing image 2/30: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:03 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:55:04 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:55:09 - processor - INFO - Reference hair color: black
2025-09-29 01:55:09 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:17 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:17 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:55:17 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:55:18 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884 "HTTP/1.1 200 OK"
2025-09-29 01:55:47 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:55:50 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978394939336_64462038780_seedream_20250929_015547_1.jpg
2025-09-29 01:55:50 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:50 - utils.logger - INFO - Processing images: 2/30 (6.7%) - ETA: 1249s - Completed: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:50 - processor - INFO - Processing image 3/30: moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:55:50 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:55:50 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
