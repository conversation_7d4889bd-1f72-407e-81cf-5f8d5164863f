2025-09-29 01:53:54 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:53:59 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:54:00 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:54:21 - api_clients.gemini_client - INFO - Initialized Gemini client with model: gemini-2.5-flash
2025-09-29 01:54:21 - api_clients.fal_client - INFO - Initialized Fal.ai client for Seedream v4
2025-09-29 01:54:21 - processor - INFO - Starting batch processing of folder: ./input [OVERRIDE: Using auto_4K]
2025-09-29 01:54:21 - utils.image_utils - INFO - Found 30 images in input
2025-09-29 01:54:21 - processor - INFO - Found 30 images to process
2025-09-29 01:54:21 - processor - INFO - Processing image 1/30: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:21 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:54:21 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:54:25 - processor - INFO - Reference hair color: black
2025-09-29 01:54:25 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:28 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:54:28 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:54:28 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:54:29 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:54:29 - api_clients.fal_client - INFO - Total encoded images size: 1.08 MB
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:54:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:55:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/d2d78ace-da66-4d85-835c-693b38d15cb3 "HTTP/1.1 200 OK"
2025-09-29 01:55:01 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:55:03 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978386344976_64462038780_seedream_20250929_015501_1.jpg
2025-09-29 01:55:03 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:55:03 - utils.logger - INFO - Processing images: 1/30 (3.3%) - ETA: 1236s - Completed: moaishahfey_1754949756_3697108978386344976_64462038780.jpg
2025-09-29 01:55:03 - processor - INFO - Processing image 2/30: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:03 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:55:04 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:55:09 - processor - INFO - Reference hair color: black
2025-09-29 01:55:09 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:17 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:17 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:55:17 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:55:18 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:55:18 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:55:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/9982f9b7-7ef4-4f5b-b8e6-53ad8bcbe884 "HTTP/1.1 200 OK"
2025-09-29 01:55:47 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:55:50 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978394939336_64462038780_seedream_20250929_015547_1.jpg
2025-09-29 01:55:50 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:50 - utils.logger - INFO - Processing images: 2/30 (6.7%) - ETA: 1249s - Completed: moaishahfey_1754949756_3697108978394939336_64462038780.jpg
2025-09-29 01:55:50 - processor - INFO - Processing image 3/30: moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:55:50 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:55:50 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:55:55 - processor - INFO - Reference hair color: black
2025-09-29 01:55:55 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:55:59 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:55:59 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:55:59 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:56:00 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:56:00 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:56:00 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:56:00 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:56:00 - api_clients.fal_client - INFO - Total encoded images size: 1.46 MB
2025-09-29 01:56:01 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:56:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:56:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:57:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/0b46c511-6bfc-4a25-850b-f99ac1896a70 "HTTP/1.1 200 OK"
2025-09-29 01:57:10 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:57:12 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978403150820_64462038780_seedream_20250929_015710_1.jpg
2025-09-29 01:57:12 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:57:12 - utils.logger - INFO - Processing images: 3/30 (10.0%) - ETA: 1540s - Completed: moaishahfey_1754949756_3697108978403150820_64462038780.jpg
2025-09-29 01:57:12 - processor - INFO - Processing image 4/30: moaishahfey_1754949756_3697108978403163213_64462038780.jpg
2025-09-29 01:57:12 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:57:12 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:57:17 - processor - INFO - Reference hair color: black
2025-09-29 01:57:17 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978403163213_64462038780.jpg
2025-09-29 01:57:42 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978403163213_64462038780.jpg
2025-09-29 01:57:42 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:57:42 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:57:43 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:57:43 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:57:43 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:57:43 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:57:43 - api_clients.fal_client - INFO - Total encoded images size: 1.25 MB
2025-09-29 01:57:44 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:57:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:57:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:58:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:58:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/511c1f89-0723-4ef8-b131-1e5b493a728a "HTTP/1.1 200 OK"
2025-09-29 01:58:38 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:58:39 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978403163213_64462038780_seedream_20250929_015838_1.jpg
2025-09-29 01:58:39 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978403163213_64462038780.jpg
2025-09-29 01:58:39 - utils.logger - INFO - Processing images: 4/30 (13.3%) - ETA: 1680s - Completed: moaishahfey_1754949756_3697108978403163213_64462038780.jpg
2025-09-29 01:58:39 - processor - INFO - Processing image 5/30: moaishahfey_1754949756_3697108978403278695_64462038780.jpg
2025-09-29 01:58:39 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:58:39 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:58:44 - processor - INFO - Reference hair color: black
2025-09-29 01:58:44 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978403278695_64462038780.jpg
2025-09-29 01:58:59 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978403278695_64462038780.jpg
2025-09-29 01:58:59 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 01:58:59 - processor - INFO - Using 5 reference image(s)
2025-09-29 01:58:59 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 01:58:59 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 01:58:59 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 01:58:59 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 01:58:59 - api_clients.fal_client - INFO - Total encoded images size: 1.20 MB
2025-09-29 01:59:00 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 01:59:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 01:59:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 01:59:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/da73beb6-03f1-4a8e-afbb-025e021e8ad8 "HTTP/1.1 200 OK"
2025-09-29 01:59:45 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 01:59:47 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978403278695_64462038780_seedream_20250929_015945_1.jpg
2025-09-29 01:59:47 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978403278695_64462038780.jpg
2025-09-29 01:59:47 - utils.logger - INFO - Processing images: 5/30 (16.7%) - ETA: 1632s - Completed: moaishahfey_1754949756_3697108978403278695_64462038780.jpg
2025-09-29 01:59:47 - processor - INFO - Processing image 6/30: moaishahfey_1754949756_3697108978403301856_64462038780.jpg
2025-09-29 01:59:47 - processor - INFO - Analyzing input image with Gemini...
2025-09-29 01:59:47 - api_clients.gemini_client - INFO - Analyzing hair color from: /home/<USER>/Dev/AIFM/ig2seed/character reference/ComfyUI_00049_.png
2025-09-29 01:59:52 - processor - INFO - Reference hair color: black
2025-09-29 01:59:52 - api_clients.gemini_client - INFO - Analyzing image: input/moaishahfey_1754949756_3697108978403301856_64462038780.jpg
2025-09-29 02:00:07 - api_clients.gemini_client - INFO - Successfully analyzed image: moaishahfey_1754949756_3697108978403301856_64462038780.jpg
2025-09-29 02:00:07 - processor - INFO - Generating 1 image(s) with Seedream...
2025-09-29 02:00:07 - processor - INFO - Using 5 reference image(s)
2025-09-29 02:00:07 - processor - INFO - Reference images within limit at 0.85 MB
2025-09-29 02:00:07 - api_clients.fal_client - INFO - Generating 1 image(s) with edit model
2025-09-29 02:00:07 - api_clients.fal_client - INFO - Using 5 pre-encoded reference images
2025-09-29 02:00:07 - api_clients.fal_client - INFO - Added input image for scene/pose reference
2025-09-29 02:00:07 - api_clients.fal_client - INFO - Total encoded images size: 1.14 MB
2025-09-29 02:00:08 - httpx - INFO - HTTP Request: POST https://queue.fal.run/fal-ai/bytedance/seedream/v4/edit "HTTP/1.1 200 OK"
2025-09-29 02:00:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:16 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:17 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:18 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:19 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:20 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:21 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:22 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:23 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:24 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:25 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:26 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:27 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:28 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:29 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:30 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:31 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:32 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:33 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:34 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:35 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:36 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:37 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:38 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:39 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:40 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:41 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:42 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:43 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:44 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:45 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:46 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:47 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:48 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:49 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:50 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:51 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:52 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:53 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:54 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:55 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:56 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:57 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:58 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:00:59 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:00 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:01 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:02 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:03 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:04 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:05 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:06 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:07 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:08 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:09 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:10 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:11 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:12 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:13 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:14 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 202 Accepted"
2025-09-29 02:01:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc/status?logs=false "HTTP/1.1 200 OK"
2025-09-29 02:01:15 - httpx - INFO - HTTP Request: GET https://queue.fal.run/fal-ai/bytedance/requests/5ef2e1f3-5280-42ea-bcde-8031a4d98ccc "HTTP/1.1 200 OK"
2025-09-29 02:01:15 - api_clients.fal_client - INFO - Successfully generated 1 image(s) with reference character
2025-09-29 02:01:18 - utils.image_utils - INFO - Downloaded image to: output/moaishahfey_1754949756_3697108978403301856_64462038780_seedream_20250929_020115_1.jpg
2025-09-29 02:01:18 - processor - INFO - Successfully generated 1 images for moaishahfey_1754949756_3697108978403301856_64462038780.jpg
2025-09-29 02:01:18 - utils.logger - INFO - Processing images: 6/30 (20.0%) - ETA: 1666s - Completed: moaishahfey_1754949756_3697108978403301856_64462038780.jpg
