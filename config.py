import os
from dotenv import load_dotenv
from typing import Optional

load_dotenv()


class Config:
    GEMINI_API_KEY: str = os.getenv('GEMINI_API_KEY', '')
    FAL_API_KEY: str = os.getenv('FAL_API_KEY', '')

    DEFAULT_INPUT_FOLDER: str = os.getenv('DEFAULT_INPUT_FOLDER', './input')
    DEFAULT_OUTPUT_FOLDER: str = os.getenv('DEFAULT_OUTPUT_FOLDER', './output')

    DEFAULT_IMAGE_SIZE: str = os.getenv('DEFAULT_IMAGE_SIZE', 'auto_4K')
    DEFAULT_IMAGE_COUNT: int = int(os.getenv('DEFAULT_IMAGE_COUNT', '1'))

    GEMINI_MODEL: str = os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')
    SEEDREAM_TEXT_MODEL: str = os.getenv('SEEDREAM_TEXT_MODEL', 'fal-ai/bytedance/seedream/v4/text-to-image')
    SEEDREAM_EDIT_MODEL: str = os.getenv('SEEDREAM_EDIT_MODEL', 'fal-ai/bytedance/seedream/v4/edit')

    AVAILABLE_SIZES = [
        "square_hd", "square", "portrait_4_3", "portrait_16_9",
        "landscape_4_3", "landscape_16_9", "auto", "auto_2K", "auto_4K",
        "1024x1024", "1280x1280", "1536x1536", "2048x2048",
        "1280x720", "1920x1080", "2560x1440", "3840x2160"
    ]

    DEFAULT_GEMINI_PROMPT = """Analyze this INPUT image that will be kept EXACTLY as is, with only the person's face swapped.

Hair color from reference: [HAIR_COLOR]

**Your Task:**
Describe the EXACT scene from this input image:
1. Type of shot
2. Expression and gaze (from input)
3. Exact pose (from input)
4. Exact clothing (from input)
5. EXACT background/scene (from input)
6. EXACT lighting (from input)

**Format:**
"[HAIR_COLOR] woman in [exact scene description from input image], amateur candid, raw phone photo"

Keep it under 40 words. The background MUST be preserved exactly.
Always include: amateur candid, JPEG artifacts, slight film grain

---

**CRITICAL INSTRUCTIONS:**
- This is a FACE SWAP operation
- The INPUT IMAGE's background/scene must be PRESERVED EXACTLY
- Only the person's face/hair changes to match reference
- Describe the EXACT scene you see in this input image
- The output should look like this input image with only face swapped
- The model should always be Latina / White

---

**Example Outputs:**

"Blonde woman in bathroom selfie, neutral expression, looking at phone, wearing black tank top, exact bathroom with white tiles, amateur candid, JPEG artifacts, slight film grain"

"Brunette woman in city street, smiling, standing pose, beige crop top, exact blue skyline with street lights, raw phone photo, amateur candid, slight grain"

"Blonde woman in living room, sitting on gray couch, white sweater, exact background with plants, amateur candid, JPEG artifacts, handheld camera"

Remember: Always add phone photo keywords!
"""

    @classmethod
    def validate(cls) -> tuple[bool, list[str]]:
        """Validate configuration"""
        errors = []

        if not cls.GEMINI_API_KEY:
            errors.append("GEMINI_API_KEY is not set")
        if not cls.FAL_API_KEY:
            errors.append("FAL_API_KEY is not set")

        return len(errors) == 0, errors

    @classmethod
    def update_api_keys(cls, gemini_key: Optional[str] = None, fal_key: Optional[str] = None):
        """Update API keys dynamically"""
        if gemini_key:
            cls.GEMINI_API_KEY = gemini_key
            os.environ['GEMINI_API_KEY'] = gemini_key
        if fal_key:
            cls.FAL_API_KEY = fal_key
            os.environ['FAL_API_KEY'] = fal_key
