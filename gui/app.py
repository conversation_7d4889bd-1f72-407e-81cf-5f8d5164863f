from __future__ import annotations

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

from PySide6 import QtCore, QtGui, QtWidgets

sys.path.append(str(Path(__file__).resolve().parent.parent))

from config import Config
from processor import ImageGenerationProcessor
from utils import gui_handler, setup_logger, ImageProcessor

from .dialogs import SizeLimitDialog

APP_TITLE = "IG2Seed - Instagram to Seedream Converter"


def create_pixmap(path: Optional[str], max_size: QtCore.QSize) -> Optional[QtGui.QPixmap]:
    """Load an image from disk and return a scaled pixmap."""
    if not path or not os.path.exists(path):
        return None

    pixmap = QtGui.QPixmap(path)
    if pixmap.isNull():
        return None

    return pixmap.scaled(
        max_size,
        QtCore.Qt.AspectRatioMode.KeepAspectRatio,
        QtCore.Qt.TransformationMode.SmoothTransformation,
    )


class ProcessorWorker(QtCore.QObject):
    """Background worker that wraps ImageGenerationProcessor."""

    progress = QtCore.Signal(int, int, float, str)
    stage = QtCore.Signal(str, dict)
    result_ready = QtCore.Signal(dict)
    error = QtCore.Signal(str)
    finished = QtCore.Signal()

    def __init__(
        self,
        input_folder: str,
        output_folder: str,
        image_size: str,
        num_images: int,
        references: List[str],
        prompt: Optional[str],
        seed: Optional[int],
        gemini_key: str,
        fal_key: str,
        parent: Optional[QtCore.QObject] = None,
    ) -> None:
        super().__init__(parent)
        self._input_folder = input_folder
        self._output_folder = output_folder
        self._image_size = image_size
        self._num_images = num_images
        self._references = references
        self._prompt = prompt
        self._seed = seed
        self._gemini_key = gemini_key
        self._fal_key = fal_key
        self._processor: Optional[ImageGenerationProcessor] = None

    @QtCore.Slot()
    def run(self) -> None:
        try:
            Config.update_api_keys(self._gemini_key, self._fal_key)

            self._processor = ImageGenerationProcessor(
                gemini_key=self._gemini_key,
                fal_key=self._fal_key,
                parent_window=None,
            )

            self._processor.set_progress_callback(self._emit_progress)
            self._processor.set_stage_callback(self._emit_stage)

            results = self._processor.process_folder(
                input_folder=self._input_folder,
                output_folder=self._output_folder,
                image_size=self._image_size,
                num_images_per_input=self._num_images,
                reference_images=self._references,
                custom_gemini_prompt=self._prompt,
                seed=self._seed,
            )

            self.result_ready.emit(results)
        except Exception as exc:  # noqa: BLE001 - surfaced to UI
            self.error.emit(str(exc))
        finally:
            self.finished.emit()

    def request_cancel(self) -> None:
        if not self._processor:
            return
        try:
            self._processor.request_cancel()
        except Exception:
            pass

    def _emit_progress(self, current: int, total: int, percent: float, message: Optional[str]) -> None:
        self.progress.emit(current, total, percent or 0.0, message or "")

    def _emit_stage(self, event: str, payload: Dict) -> None:
        self.stage.emit(event, payload)


class ReferenceDropArea(QtWidgets.QFrame):
    """Widget that accepts drag & drop of image files."""

    files_selected = QtCore.Signal(list)

    def __init__(self, parent: Optional[QtWidgets.QWidget] = None) -> None:
        super().__init__(parent)
        self.setObjectName("referenceDropArea")
        self.setAcceptDrops(True)
        self.setCursor(QtGui.QCursor(QtCore.Qt.CursorShape.PointingHandCursor))
        self.setProperty("dragOver", False)

        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(24, 28, 24, 24)
        layout.setSpacing(10)
        layout.addStretch()

        icon = QtWidgets.QLabel("DROP")
        icon.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        icon.setStyleSheet("font-size: 18px; letter-spacing: 4px;")
        layout.addWidget(icon)

        headline = QtWidgets.QLabel("Drop images or click to browse")
        headline.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        headline.setObjectName("dropHeadline")
        layout.addWidget(headline)

        subtitle = QtWidgets.QLabel("PNG, JPG, WEBP • up to 5 files • ≤ 4MB total")
        subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        subtitle.setObjectName("dropSubtitle")
        subtitle.setWordWrap(True)
        layout.addWidget(subtitle)
        layout.addStretch()

    def dragEnterEvent(self, event: QtGui.QDragEnterEvent) -> None:  # noqa: N802 - Qt naming
        if event.mimeData().hasUrls():
            self.setProperty("dragOver", True)
            self.style().unpolish(self)
            self.style().polish(self)
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragLeaveEvent(self, event: QtGui.QDragLeaveEvent) -> None:  # noqa: N802 - Qt naming
        self.setProperty("dragOver", False)
        self.style().unpolish(self)
        self.style().polish(self)
        event.accept()

    def dropEvent(self, event: QtGui.QDropEvent) -> None:  # noqa: N802 - Qt naming
        self.setProperty("dragOver", False)
        self.style().unpolish(self)
        self.style().polish(self)
        urls = event.mimeData().urls()
        files = [str(url.toLocalFile()) for url in urls if url.isLocalFile()]
        if files:
            self.files_selected.emit(files)
        event.acceptProposedAction()

    def mousePressEvent(self, event: QtGui.QMouseEvent) -> None:  # noqa: N802 - Qt naming
        if event.button() != QtCore.Qt.MouseButton.LeftButton:
            return
        dialog = QtWidgets.QFileDialog(self)
        dialog.setWindowTitle("Select reference images")
        dialog.setFileMode(QtWidgets.QFileDialog.FileMode.ExistingFiles)
        dialog.setNameFilters(["Images (*.png *.jpg *.jpeg *.webp *.bmp *.gif)"])
        if dialog.exec():
            files = dialog.selectedFiles()
            if files:
                self.files_selected.emit(files)


class SeedreamWindow(QtWidgets.QMainWindow):
    def __init__(self) -> None:
        super().__init__()
        self.setWindowTitle(APP_TITLE)
        self.resize(1280, 860)
        self.setMinimumSize(1100, 720)
        self.colors = {
            'background': '#080B14',
            'panel': '#0F172A',
            'card': '#111C2E',
            'hero': '#111C34',
            'accent': '#6366f1',
            'accent_dark': '#4f46e5',
            'accent_border': '#4c51bf',
            'divider': '#1f2a44',
            'muted': '#94a3b8',
            'muted_strong': '#cbd5f5'
        }
        self._configure_palette()

        self.input_folder = Config.DEFAULT_INPUT_FOLDER
        self.output_folder = Config.DEFAULT_OUTPUT_FOLDER
        self.reference_images: List[str] = []
        self.history: List[Dict] = []
        self.detected_size_info: Optional[Dict[str, Any]] = None
        self.gemini_key = Config.GEMINI_API_KEY
        self.fal_key = Config.FAL_API_KEY
        self.image_size = Config.DEFAULT_IMAGE_SIZE
        self.num_images = str(Config.DEFAULT_IMAGE_COUNT)
        self.seed_value = "-1"
        self.gemini_prompt = Config.DEFAULT_GEMINI_PROMPT

        self._worker_thread: Optional[QtCore.QThread] = None
        self._worker: Optional[ProcessorWorker] = None
        self._processing = False
        self._cancel_requested = False
        self._log_callback = None
        self._blank_pixmap = QtGui.QPixmap(1, 1)
        self._blank_pixmap.fill(QtGui.QColor("#1f2937"))
        self.current_target_size: Optional[str] = None

        self.current_aspect_info: Optional[Dict[str, Any]] = None

        self._setup_ui()
        self._setup_logging()
        self.load_settings()

    # ------------------------------------------------------------------
    # UI setup helpers
    # ------------------------------------------------------------------
    def _configure_palette(self) -> None:
        rules = [
            "QWidget {{ color: #e5e7eb; }}",
            "QWidget#mainContainer {{ background-color: {background}; font-family: 'Inter', 'Segoe UI', 'Sans Serif'; }}",
            "QMainWindow {{ background-color: {background}; }}",
            "QTabWidget {{ background: transparent; }}",
            "QTabWidget::pane {{ border: none; background: transparent; }}",
            "QTabWidget::tab-bar {{ alignment: left; left: 16px; }}",
            "QTabBar::tab {{ background: {card}; color: {muted_strong}; padding: 12px 26px; margin-right: 14px; border-radius: 12px 12px 0 0; min-width: 150px; font-weight: 600; }}",
            "QTabBar::tab:selected {{ background: {accent}; color: #ffffff; }}",
            "QTabBar::tab:hover {{ background: {accent_dark}; color: #ffffff; }}",
            "QFrame#heroStrip {{ background-color: {hero}; border: 1px solid {divider}; border-radius: 20px; }}",
            "QFrame#card {{ background-color: {card}; border: 1px solid {divider}; border-radius: 20px; }}",
            "QFrame#heroCard {{ background-color: {hero}; border: 1px solid {divider}; border-radius: 24px; }}",
            "QFrame#dropShell {{ background-color: rgba(86, 89, 148, 0.18); border: 1px dashed {accent_border}; border-radius: 22px; }}",
            "QFrame#dropShell QLabel {{ color: {muted_strong}; }}",
            "QFrame#referenceDropArea {{ background-color: rgba(86, 89, 148, 0.16); border: 1px dashed {accent_border}; border-radius: 22px; }}",
            "QFrame#referenceDropArea[dragOver=\"true\"] {{ background-color: rgba(99, 102, 241, 0.28); border-color: {accent}; }}",
            "QFrame#galleryContainer {{ background-color: transparent; border: none; }}",
            "QFrame#thumbCard {{ background-color: rgba(32, 40, 66, 0.72); border: 1px solid {divider}; border-radius: 18px; }}",
            "QFrame#thumbCard QLabel {{ color: {muted_strong}; }}",
            "QFrame#thumbCard QPushButton {{ padding: 6px 10px; }}",
            "QLabel#dropHeadline {{ font-size: 16px; font-weight: 600; color: #f8fafc; }}",
            "QLabel#dropSubtitle {{ color: {muted}; font-size: 12px; }}",
            "QLabel.heroTitle {{ font-size: 28px; font-weight: 700; color: #f9fafc; }}",
            "QLabel.heroSubtitle {{ color: {muted}; font-size: 13px; }}",
            "QLabel.sectionTitle {{ font-size: 16px; font-weight: 600; }}",
            "QLabel.sectionHint {{ color: {muted}; font-size: 12px; }}",
            "QPushButton[class=\"primary\"] {{ background-color: {accent}; border: none; border-radius: 14px; padding: 12px 20px; color: white; font-weight: 600; }}",
            "QPushButton[class=\"primary\"]:hover {{ background-color: {accent_dark}; }}",
            "QPushButton[class=\"primary\"]:disabled {{ background-color: rgba(99,102,241,0.32); color: {muted}; }}",
            "QPushButton[class=\"outline\"] {{ border: 1px solid {accent_border}; border-radius: 14px; padding: 10px 18px; background-color: transparent; color: {muted_strong}; }}",
            "QPushButton[class=\"outline\"]:hover {{ border-color: {accent}; color: #ffffff; }}",
            "QPushButton[class=\"danger\"] {{ background-color: #ef4444; border: none; border-radius: 14px; padding: 10px 18px; color: white; font-weight: 600; }}",
            "QLineEdit, QSpinBox, QComboBox, QTextEdit, QPlainTextEdit {{ background-color: #0b1220; border: 1px solid {divider}; border-radius: 14px; padding: 10px 14px; color: #e5e7eb; selection-background-color: {accent_dark}; }}",
            "QScrollArea {{ border: none; background: transparent; }}",
            "QScrollArea QWidget {{ background: transparent; }}",
            "QListWidget#historyList {{ background-color: transparent; border: none; }}",
            "QListWidget#historyList::item {{ margin: 4px 2px; padding: 8px 10px; border-radius: 12px; color: {muted_strong}; }}",
            "QListWidget#historyList::item:selected {{ background-color: rgba(99,102,241,0.24); color: #ffffff; }}",
            "QProgressBar {{ background-color: {panel}; border-radius: 16px; border: 1px solid {divider}; height: 20px; }}",
            "QProgressBar::chunk {{ background-color: {accent}; border-radius: 16px; }}",
            "QFrame.imageWell {{ background-color: #0b1220; border: 1px solid {divider}; border-radius: 18px; }}",
            "QLabel.statusPill {{ background-color: rgba(99,102,241,0.28); color: {muted_strong}; border-radius: 12px; padding: 4px 10px; font-size: 11px; font-weight: 600; }}",
            "QPushButton.linkButton {{ color: #60a5fa; background: transparent; border: none; text-decoration: underline; font-weight: 600; }}",
            "QLabel.badgeSuccess {{ background-color: #16a34a; padding: 4px 10px; border-radius: 12px; font-size: 11px; font-weight: 600; color: white; }}",
            "QLabel.badgeWarning {{ background-color: #ca8a04; padding: 4px 10px; border-radius: 12px; font-size: 11px; font-weight: 600; color: white; }}",
            "QLabel.badgeError {{ background-color: #dc2626; padding: 4px 10px; border-radius: 12px; font-size: 11px; font-weight: 600; color: white; }}"
        ]
        self.setStyleSheet(("\n".join(rules)).format(**self.colors))

    def _setup_ui(self) -> None:
        container = QtWidgets.QWidget()
        container.setObjectName("mainContainer")
        self.setCentralWidget(container)

        layout = QtWidgets.QVBoxLayout(container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(18)

        header = QtWidgets.QFrame()
        header.setObjectName("heroStrip")
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(24, 18, 24, 18)
        header_layout.setSpacing(12)

        title = QtWidgets.QLabel("IG2Seed")
        title.setProperty("class", "heroTitle")
        header_layout.addWidget(title)

        subtitle = QtWidgets.QLabel("Instagram to Seedream studio")
        subtitle.setProperty("class", "heroSubtitle")
        header_layout.addWidget(subtitle)
        header_layout.addStretch()

        layout.addWidget(header, 0)

        self.tabs = QtWidgets.QTabWidget()
        self.tabs.setDocumentMode(True)
        self.tabs.setTabPosition(QtWidgets.QTabWidget.TabPosition.North)
        layout.addWidget(self.tabs, 1)

        self._init_workflow_tab()
        self._init_settings_tab()
        self._init_logs_tab()

    def _init_workflow_tab(self) -> None:
        self.workflow_tab = QtWidgets.QWidget()
        self.tabs.addTab(self.workflow_tab, "Workflow")

        workflow_layout = QtWidgets.QVBoxLayout(self.workflow_tab)
        workflow_layout.setContentsMargins(12, 12, 12, 12)
        workflow_layout.setSpacing(18)

        header = QtWidgets.QLabel("<h2 style='color:#f8fafc;'>IG2Seed Workflow</h2>" "<p style='color:#94a3b8;'>Configure your run, then launch the live dashboard.</p>")
        header.setTextFormat(QtCore.Qt.TextFormat.RichText)
        workflow_layout.addWidget(header)

        self.workflow_stack = QtWidgets.QStackedWidget()
        workflow_layout.addWidget(self.workflow_stack, 1)

        self.setup_page = QtWidgets.QWidget()
        self.processing_page = QtWidgets.QWidget()
        self.workflow_stack.addWidget(self.setup_page)
        self.workflow_stack.addWidget(self.processing_page)

        self._build_setup_page()
        self._build_processing_page()
        self.workflow_stack.setCurrentWidget(self.setup_page)

    def _build_setup_page(self) -> None:
        layout = QtWidgets.QVBoxLayout(self.setup_page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QtWidgets.QFrame.Shape.NoFrame)
        layout.addWidget(scroll_area)

        content = QtWidgets.QWidget()
        scroll_area.setWidget(content)

        content_layout = QtWidgets.QVBoxLayout(content)
        content_layout.setContentsMargins(24, 24, 24, 24)
        content_layout.setSpacing(24)

        hero = QtWidgets.QFrame()
        hero.setObjectName("heroCard")
        hero_layout = QtWidgets.QVBoxLayout(hero)
        hero_layout.setContentsMargins(24, 24, 24, 24)
        hero_layout.setSpacing(12)

        hero_title = QtWidgets.QLabel("Plan a Seedream run")
        hero_title.setProperty("class", "heroTitle")
        hero_layout.addWidget(hero_title)

        hero_sub = QtWidgets.QLabel("Drop the Instagram shots, confirm defaults, and launch the live dashboard when ready.")
        hero_sub.setWordWrap(True)
        hero_sub.setProperty("class", "heroSubtitle")
        hero_layout.addWidget(hero_sub)

        self.hero_hint = QtWidgets.QLabel("No reference images yet. Drag a few portraits to get started.")
        self.hero_hint.setProperty("class", "sectionHint")
        hero_layout.addWidget(self.hero_hint)
        hero_layout.addStretch()

        content_layout.addWidget(hero)

        columns = QtWidgets.QHBoxLayout()
        columns.setSpacing(24)
        columns.setContentsMargins(0, 0, 0, 0)
        content_layout.addLayout(columns)

        drop_card = QtWidgets.QFrame()
        drop_card.setObjectName("card")
        drop_layout = QtWidgets.QVBoxLayout(drop_card)
        drop_layout.setContentsMargins(24, 24, 24, 24)
        drop_layout.setSpacing(18)

        drop_title = QtWidgets.QLabel("Reference images")
        drop_title.setProperty("class", "sectionTitle")
        drop_layout.addWidget(drop_title)

        drop_sub = QtWidgets.QLabel("Drag in up to five hero shots. We auto-match their aspect ratio, and double-click thumbnails to remove them.")
        drop_sub.setWordWrap(True)
        drop_sub.setProperty("class", "sectionHint")
        drop_layout.addWidget(drop_sub)

        self.drop_area = ReferenceDropArea()
        self.drop_area.setMinimumHeight(180)
        self.drop_area.files_selected.connect(self._handle_reference_files)
        drop_layout.addWidget(self.drop_area)

        preview_well = QtWidgets.QFrame()
        preview_well.setObjectName("imageWell")
        preview_layout = QtWidgets.QVBoxLayout(preview_well)
        preview_layout.setContentsMargins(12, 12, 12, 12)
        self.reference_preview = QtWidgets.QLabel("Preview appears once images are added")
        self.reference_preview.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.reference_preview.setMinimumHeight(160)
        preview_layout.addWidget(self.reference_preview)
        drop_layout.addWidget(preview_well)

        self.file_count_label = QtWidgets.QLabel("Drop up to five images to guide the run.")
        self.file_count_label.setProperty("class", "sectionHint")

        meta_row = QtWidgets.QHBoxLayout()
        meta_row.addWidget(self.file_count_label)
        meta_row.addStretch()
        self.clear_refs_button = QtWidgets.QPushButton("Clear references")
        self.clear_refs_button.setProperty("class", "outline")
        self.clear_refs_button.setEnabled(False)
        self.clear_refs_button.clicked.connect(self._clear_reference_images)
        meta_row.addWidget(self.clear_refs_button)
        drop_layout.addLayout(meta_row)

        gallery_frame = QtWidgets.QFrame()
        gallery_frame.setObjectName("galleryContainer")
        gallery_layout = QtWidgets.QGridLayout(gallery_frame)
        gallery_layout.setContentsMargins(0, 0, 0, 0)
        gallery_layout.setHorizontalSpacing(16)
        gallery_layout.setVerticalSpacing(16)
        gallery_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignTop)
        self.gallery_grid = gallery_layout
        drop_layout.addWidget(gallery_frame)

        columns.addWidget(drop_card, 7)

        side_container = QtWidgets.QVBoxLayout()
        side_container.setSpacing(18)
        columns.addLayout(side_container, 5)

        folders_card, folders_body = self._create_card("Project folders", "Select where we read Instagram posts and where Seedream saves outputs.")
        side_container.addWidget(folders_card)

        self.input_path_edit = QtWidgets.QLineEdit(self.input_folder)
        self.input_path_edit.setPlaceholderText("Source folder with downloaded posts")
        folders_body.addLayout(self._build_browse_row("Input folder", self.input_path_edit, self._select_input_folder))

        self.output_path_edit = QtWidgets.QLineEdit(self.output_folder)
        self.output_path_edit.setPlaceholderText("Where Seedream outputs should be saved")
        folders_body.addLayout(self._build_browse_row("Output folder", self.output_path_edit, self._select_output_folder))

        settings_card, settings_body = self._create_card("Generation defaults", "Tune how many outputs to create and how we scale each shot.")
        side_container.addWidget(settings_card)

        settings_grid = QtWidgets.QGridLayout()
        settings_grid.setHorizontalSpacing(14)
        settings_grid.setVerticalSpacing(12)
        settings_body.addLayout(settings_grid)

        settings_grid.addWidget(QtWidgets.QLabel("Image size"), 0, 0)
        self.size_combo = QtWidgets.QComboBox()
        for size in Config.AVAILABLE_SIZES:
            if size == "auto_4K":
                self.size_combo.addItem("Match input (max 4K)", size)
            else:
                self.size_combo.addItem(size, size)
        index = self.size_combo.findData(self.image_size)
        if index == -1:
            index = 0
        self.size_combo.setCurrentIndex(index)
        self.size_combo.currentIndexChanged.connect(lambda _=None: self._refresh_aspect_hint())
        settings_grid.addWidget(self.size_combo, 0, 1)

        settings_grid.addWidget(QtWidgets.QLabel("Images per post"), 1, 0)
        self.count_spin = QtWidgets.QSpinBox()
        self.count_spin.setRange(1, 20)
        try:
            self.count_spin.setValue(int(self.num_images))
        except Exception:
            self.count_spin.setValue(1)
        settings_grid.addWidget(self.count_spin, 1, 1)

        settings_grid.addWidget(QtWidgets.QLabel("Seed (-1 random)"), 2, 0)
        self.seed_edit = QtWidgets.QLineEdit(self.seed_value)
        settings_grid.addWidget(self.seed_edit, 2, 1)

        self.auto_size_hint = QtWidgets.QLabel("")
        self.auto_size_hint.setProperty("class", "sectionHint")
        self.auto_size_hint.setWordWrap(True)
        settings_body.addWidget(self.auto_size_hint)

        actions_card, actions_body = self._create_card("Ready to generate", None)
        side_container.addWidget(actions_card)

        cta_row = QtWidgets.QHBoxLayout()
        cta_row.setSpacing(12)

        self.start_button = QtWidgets.QPushButton("Start processing")
        self.start_button.setObjectName("startButton")
        self.start_button.setProperty("class", "primary")
        self.start_button.setCursor(QtGui.QCursor(QtCore.Qt.CursorShape.PointingHandCursor))
        self.start_button.clicked.connect(self.start_processing)
        cta_row.addWidget(self.start_button, 0)

        actions_body.addLayout(cta_row)

        summary = QtWidgets.QLabel("• Auto-aspect matches keep Seedream outputs aligned\n• Random seed (-1) keeps results fresh\n• Results and prompts stream live on the next tab")
        summary.setProperty("class", "sectionHint")
        summary.setWordWrap(True)
        actions_body.addWidget(summary)

        side_container.addStretch()
        content_layout.addStretch()

        self._update_reference_display()

    def _build_processing_page(self) -> None:
        layout = QtWidgets.QVBoxLayout(self.processing_page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QtWidgets.QFrame.Shape.NoFrame)
        layout.addWidget(scroll_area)

        content = QtWidgets.QWidget()
        scroll_area.setWidget(content)

        content_layout = QtWidgets.QVBoxLayout(content)
        content_layout.setContentsMargins(24, 24, 24, 24)
        content_layout.setSpacing(20)

        hero = QtWidgets.QFrame()
        hero.setObjectName("heroCard")
        hero_layout = QtWidgets.QHBoxLayout(hero)
        hero_layout.setContentsMargins(24, 20, 24, 20)
        hero_layout.setSpacing(18)

        hero_copy = QtWidgets.QVBoxLayout()
        hero_copy.setSpacing(6)
        self.processing_title = QtWidgets.QLabel("Processing dashboard")
        self.processing_title.setProperty("class", "heroTitle")
        hero_copy.addWidget(self.processing_title)
        hero_sub = QtWidgets.QLabel("Monitor prompts, renders, and completion stats in real time.")
        hero_sub.setWordWrap(True)
        hero_sub.setProperty("class", "heroSubtitle")
        hero_copy.addWidget(hero_sub)
        hero_copy.addStretch()
        hero_layout.addLayout(hero_copy, 1)

        controls = QtWidgets.QHBoxLayout()
        controls.setSpacing(10)
        controls.addStretch()
        self.back_button = QtWidgets.QPushButton("Return to setup")
        self.back_button.setProperty("class", "outline")
        self.back_button.setCursor(QtGui.QCursor(QtCore.Qt.CursorShape.PointingHandCursor))
        self.back_button.setToolTip("Switch back to the setup view")
        self.back_button.clicked.connect(self.show_setup_view)
        controls.addWidget(self.back_button)

        self.cancel_button = QtWidgets.QPushButton("Stop run")
        self.cancel_button.setProperty("class", "danger")
        self.cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.CursorShape.PointingHandCursor))
        self.cancel_button.setToolTip("Cancel after the current image finishes")
        self.cancel_button.clicked.connect(self.cancel_processing)
        self.cancel_button.setEnabled(False)
        controls.addWidget(self.cancel_button)

        hero_layout.addLayout(controls, 0)
        content_layout.addWidget(hero)

        body_layout = QtWidgets.QHBoxLayout()
        body_layout.setSpacing(20)
        content_layout.addLayout(body_layout)

        left_column = QtWidgets.QVBoxLayout()
        left_column.setSpacing(18)
        body_layout.addLayout(left_column, 4)

        status_card, status_body = self._create_card("Run status", "Overall progress across the batch.")
        left_column.addWidget(status_card)

        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setRange(0, 1)
        status_body.addWidget(self.progress_bar)

        self.status_label = QtWidgets.QLabel("Run idle")
        self.status_label.setProperty("class", "sectionHint")
        status_body.addWidget(self.status_label)

        self.stats_label = QtWidgets.QLabel("Processed: 0\nSuccess: 0 | Failed: 0")
        self.stats_label.setProperty("class", "sectionHint")
        status_body.addWidget(self.stats_label)

        activity_card, activity_body = self._create_card("Live activity", "Gemini + Seedream updates for the active shot.")
        left_column.addWidget(activity_card, 4)

        header_row = QtWidgets.QHBoxLayout()
        header_row.addWidget(QtWidgets.QLabel("Current input"))
        header_row.addStretch()
        header_row.addWidget(QtWidgets.QLabel("Seedream output"))
        activity_body.addLayout(header_row)

        wells_row = QtWidgets.QHBoxLayout()
        wells_row.setSpacing(12)

        input_well = QtWidgets.QFrame()
        input_well.setObjectName("imageWell")
        input_layout = QtWidgets.QVBoxLayout(input_well)
        input_layout.setContentsMargins(12, 12, 12, 12)
        self.current_image_label = QtWidgets.QLabel("Waiting to start")
        self.current_image_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.current_image_label.setMinimumHeight(200)
        input_layout.addWidget(self.current_image_label)
        wells_row.addWidget(input_well, 1)

        output_well = QtWidgets.QFrame()
        output_well.setObjectName("imageWell")
        output_layout = QtWidgets.QVBoxLayout(output_well)
        output_layout.setContentsMargins(12, 12, 12, 12)
        self.output_image_label = QtWidgets.QLabel("Seedream pending")
        self.output_image_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.output_image_label.setMinimumHeight(200)
        output_layout.addWidget(self.output_image_label)
        self.seedream_status = QtWidgets.QLabel("Seedream pending")
        self.seedream_status.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.seedream_status.setWordWrap(True)
        self.seedream_status.setProperty("class", "statusPill")
        output_layout.addWidget(self.seedream_status)
        wells_row.addWidget(output_well, 1)

        activity_body.addLayout(wells_row)

        caption_header = QtWidgets.QHBoxLayout()
        caption_header.addWidget(QtWidgets.QLabel("Gemini caption"))
        caption_header.addStretch()
        self.caption_status = QtWidgets.QLabel("Gemini pending")
        self.caption_status.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.caption_status.setProperty("class", "statusPill")
        caption_header.addWidget(self.caption_status)
        activity_body.addLayout(caption_header)

        self.caption_view = QtWidgets.QPlainTextEdit()
        self.caption_view.setReadOnly(True)
        self.caption_view.setPlaceholderText("Gemini captions will appear here.")
        self.caption_view.setMinimumHeight(160)
        activity_body.addWidget(self.caption_view)

        right_column = QtWidgets.QVBoxLayout()
        right_column.setSpacing(18)
        body_layout.addLayout(right_column, 5)

        latest_card, latest_body = self._create_card("Latest result", "Pinned preview and prompt from the most recent completion.")
        right_column.addWidget(latest_card, 3)

        preview_well = QtWidgets.QFrame()
        preview_well.setObjectName("imageWell")
        preview_layout = QtWidgets.QVBoxLayout(preview_well)
        preview_layout.setContentsMargins(12, 12, 12, 12)
        self.results_preview = QtWidgets.QLabel("Results will appear here")
        self.results_preview.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.results_preview.setMinimumHeight(200)
        preview_layout.addWidget(self.results_preview)
        latest_body.addWidget(preview_well)

        self.results_meta = QtWidgets.QLabel("")
        self.results_meta.setWordWrap(True)
        self.results_meta.setProperty("class", "sectionHint")
        latest_body.addWidget(self.results_meta)

        self.prompt_view = QtWidgets.QPlainTextEdit()
        self.prompt_view.setReadOnly(True)
        self.prompt_view.setPlaceholderText("Prompts and generation notes land here.")
        self.prompt_view.setMinimumHeight(140)
        latest_body.addWidget(self.prompt_view)

        buttons_row = QtWidgets.QHBoxLayout()
        self.open_image_button = QtWidgets.QPushButton("Open image")
        self.open_image_button.setProperty("class", "outline")
        self.open_image_button.clicked.connect(self._open_selected_image)
        self.open_image_button.setEnabled(False)
        buttons_row.addWidget(self.open_image_button)

        self.copy_prompt_button = QtWidgets.QPushButton("Copy prompt")
        self.copy_prompt_button.setProperty("class", "outline")
        self.copy_prompt_button.clicked.connect(self._copy_selected_prompt)
        self.copy_prompt_button.setEnabled(False)
        buttons_row.addWidget(self.copy_prompt_button)
        buttons_row.addStretch()
        latest_body.addLayout(buttons_row)

        history_card, history_body = self._create_card("Timeline", "Session history with quick jumps to previous prompts.")
        right_column.addWidget(history_card, 4)

        self.history_list = QtWidgets.QListWidget()
        self.history_list.setObjectName("historyList")
        self.history_list.setSpacing(8)
        self.history_list.itemSelectionChanged.connect(self._on_history_selection)
        self.history_list.setMinimumHeight(240)
        history_body.addWidget(self.history_list)

        content_layout.addStretch()

    def _init_settings_tab(self) -> None:
        self.settings_tab = QtWidgets.QWidget()
        self.tabs.addTab(self.settings_tab, "Settings")

        outer = QtWidgets.QVBoxLayout(self.settings_tab)
        outer.setContentsMargins(18, 18, 18, 18)
        outer.setSpacing(18)

        title = QtWidgets.QLabel("<h2 style='color:#f8fafc;'>Settings</h2>" "<p style='color:#94a3b8;'>Store your API keys and prompt template.</p>")
        title.setTextFormat(QtCore.Qt.TextFormat.RichText)
        outer.addWidget(title)

        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        outer.addWidget(scroll_area, 1)

        scroll_widget = QtWidgets.QWidget()
        scroll_area.setWidget(scroll_widget)
        layout = QtWidgets.QVBoxLayout(scroll_widget)
        layout.setSpacing(16)

        api_card, api_body = self._create_card("API keys", "Saved locally inside .env for the next launch.")
        layout.addWidget(api_card)

        self.gemini_edit = QtWidgets.QLineEdit(self.gemini_key)
        self.gemini_edit.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        api_body.addLayout(self._build_labeled_row("Gemini API key", self.gemini_edit))

        self.fal_edit = QtWidgets.QLineEdit(self.fal_key)
        self.fal_edit.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        api_body.addLayout(self._build_labeled_row("Fal.ai API key", self.fal_edit))

        test_button = QtWidgets.QPushButton("Test API connections")
        test_button.setProperty("class", "primary")
        test_button.clicked.connect(self.test_connections)
        api_body.addWidget(test_button)

        prompt_card, prompt_body = self._create_card("Gemini prompt template", "Override the default instructions sent before generation.")
        layout.addWidget(prompt_card)

        self.prompt_edit = QtWidgets.QPlainTextEdit(self.gemini_prompt)
        prompt_body.addWidget(self.prompt_edit, 1)

        actions_card, actions_body = self._create_card("Save or reset", "Persist updates or jump back to defaults.")
        layout.addWidget(actions_card)

        save_button = QtWidgets.QPushButton("Save settings")
        save_button.setProperty("class", "primary")
        save_button.clicked.connect(self.save_settings)
        actions_body.addWidget(save_button)

        reset_button = QtWidgets.QPushButton("Reset to defaults")
        reset_button.setProperty("class", "outline")
        reset_button.clicked.connect(self.reset_settings)
        actions_body.addWidget(reset_button)
        layout.addStretch()

    def _init_logs_tab(self) -> None:
        self.logs_tab = QtWidgets.QWidget()
        self.tabs.addTab(self.logs_tab, "Logs")

        layout = QtWidgets.QVBoxLayout(self.logs_tab)
        layout.setContentsMargins(18, 18, 18, 18)
        layout.setSpacing(18)

        title = QtWidgets.QLabel("<h2 style='color:#f8fafc;'>Run logs</h2>" "<p style='color:#94a3b8;'>Realtime updates from the processor.</p>")
        title.setTextFormat(QtCore.Qt.TextFormat.RichText)
        layout.addWidget(title)

        card, card_body = self._create_card("Log output", None)
        layout.addWidget(card, 1)

        self.log_view = QtWidgets.QPlainTextEdit()
        self.log_view.setReadOnly(True)
        card_body.addWidget(self.log_view, 1)

        clear_button = QtWidgets.QPushButton("Clear logs")
        clear_button.setProperty("class", "outline")
        clear_button.clicked.connect(self.log_view.clear)
        layout.addWidget(clear_button, 0, QtCore.Qt.AlignmentFlag.AlignRight)

    def _create_card(self, title: str, subtitle: Optional[str]) -> tuple[QtWidgets.QFrame, QtWidgets.QVBoxLayout]:
        card = QtWidgets.QFrame()
        card.setObjectName("card")
        card.setFrameShape(QtWidgets.QFrame.Shape.StyledPanel)
        card_layout = QtWidgets.QVBoxLayout(card)
        card_layout.setContentsMargins(18, 18, 18, 18)
        card_layout.setSpacing(14)

        header = QtWidgets.QVBoxLayout()
        header.setSpacing(4)
        if title:
            label = QtWidgets.QLabel(title)
            label.setObjectName("sectionTitle")
            header.addWidget(label)
        if subtitle:
            hint = QtWidgets.QLabel(subtitle)
            hint.setObjectName("sectionHint")
            hint.setWordWrap(True)
            header.addWidget(hint)
        card_layout.addLayout(header)

        return card, card_layout

    def _build_browse_row(
        self,
        label_text: str,
        editor: QtWidgets.QLineEdit,
        handler: Callable[[], None],
    ) -> QtWidgets.QVBoxLayout:
        wrapper = QtWidgets.QVBoxLayout()
        wrapper.setSpacing(6)

        label = QtWidgets.QLabel(label_text)
        wrapper.addWidget(label)

        row = QtWidgets.QHBoxLayout()
        row.setSpacing(8)
        row.addWidget(editor, 1)

        button = QtWidgets.QPushButton("Browse")
        button.setProperty("class", "outline")
        button.clicked.connect(handler)
        row.addWidget(button)

        wrapper.addLayout(row)
        return wrapper

    def _build_labeled_row(
        self, label_text: str, widget: QtWidgets.QWidget
    ) -> QtWidgets.QVBoxLayout:
        layout = QtWidgets.QVBoxLayout()
        layout.setSpacing(6)
        label = QtWidgets.QLabel(label_text)
        layout.addWidget(label)
        layout.addWidget(widget)
        return layout

    # ------------------------------------------------------------------
    # Setup helpers
    # ------------------------------------------------------------------
    def _setup_logging(self) -> None:
        log_path = Path("logs") / f"log_ig2seed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        setup_logger(log_file=log_path)

        def log_callback(message: str, level: str) -> None:
            self.log_view.appendPlainText(message)
            self.log_view.verticalScrollBar().setValue(self.log_view.verticalScrollBar().maximum())

        gui_handler.add_callback(log_callback)
        self._log_callback = log_callback

    # ------------------------------------------------------------------
    # Aspect ratio helpers
    # ------------------------------------------------------------------
    def _infer_aspect_from_folder(self, folder: Path) -> Optional[Dict[str, Any]]:
        try:
            images = ImageProcessor.get_images_from_folder(folder)
        except Exception:
            return None

        fallback_info: Optional[Dict[str, Any]] = None
        for image_path in images:
            info = ImageProcessor.infer_resolution_from_image(image_path)
            if not info:
                continue
            info['source_path'] = str(image_path)
            if info.get('fallback'):
                if fallback_info is None:
                    fallback_info = info
                continue
            return info
        return fallback_info

    def _refresh_aspect_hint(self) -> None:
        if not hasattr(self, 'auto_size_hint') or not hasattr(self, 'size_combo'):
            return

        folder_text = self.input_path_edit.text().strip() if hasattr(self, 'input_path_edit') else ''
        info = None
        if folder_text:
            folder = Path(folder_text)
            if folder.exists():
                info = self._infer_aspect_from_folder(folder)

        self.detected_size_info = info

        selected_value = self.size_combo.currentData() if hasattr(self.size_combo, 'currentData') else None
        if selected_value is None:
            selected_value = self.size_combo.currentText() if hasattr(self.size_combo, 'currentText') else None

        message = ''
        if selected_value == 'auto_4K':
            if info and not info.get('fallback'):
                ratio_label = info.get('ratio_label') or info.get('closest_ratio_label')
                size_text = info.get('target_size')
                source_name = Path(info.get('source_path', '')).name if info.get('source_path') else ''
                origin = f" from {source_name}" if source_name else ''
                if ratio_label:
                    message = f"Detected {ratio_label} → targeting {size_text}{origin}."
                else:
                    message = f"Targeting {size_text}{origin}."
            elif info:
                size_text = info.get('target_size', '3840x2160')
                message = f"Aspect ratio not detected; using {size_text} (closest match)."
            else:
                message = "No input images detected yet; defaulting to 3840x2160."
        else:
            display = self.size_combo.currentText() if hasattr(self.size_combo, 'currentText') else str(selected_value)
            if info and not info.get('fallback'):
                ratio_label = info.get('ratio_label') or info.get('closest_ratio_label')
                size_text = info.get('target_size')
                message = f"Manual override: {display}. Detected {ratio_label} suggests {size_text}."
            elif info:
                size_text = info.get('target_size', '3840x2160')
                message = f"Manual override: {display}. Aspect ratio detection unavailable; default match would use {size_text}."
            else:
                message = f"Manual override: {display}. No input images detected."

        self.auto_size_hint.setText(message)

    # ------------------------------------------------------------------
    # Reference management
    # ------------------------------------------------------------------
    def _remove_reference_by_path(self, path: str) -> None:
        if path in self.reference_images:
            self.reference_images.remove(path)
            self._update_reference_display()

    def _handle_reference_files(self, files: List[str]) -> None:
        valid_ext = {".png", ".jpg", ".jpeg", ".webp", ".bmp", ".gif"}
        selected = [f for f in files if Path(f).suffix.lower() in valid_ext and os.path.exists(f)]
        if not selected:
            QtWidgets.QMessageBox.information(self, "References", "No supported image files found.")
            return

        self.reference_images = selected[:5]
        if not self._check_reference_sizes():
            return
        self._update_reference_display()

    def _clear_reference_images(self) -> None:
        if not self.reference_images:
            return
        self.reference_images.clear()
        self._update_reference_display()

    def _update_reference_display(self) -> None:
        if hasattr(self, 'gallery_grid'):
            while self.gallery_grid.count():
                item = self.gallery_grid.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.setParent(None)

        if not self.reference_images:
            self.file_count_label.setText("Drop up to five images to guide the run.")
            self.reference_preview.setPixmap(QtGui.QPixmap())
            self.reference_preview.setText("Preview appears once images are added")
            if hasattr(self, 'hero_hint'):
                self.hero_hint.setText("No reference images yet. Drag a few portraits to get started.")
            if hasattr(self, 'clear_refs_button'):
                self.clear_refs_button.setEnabled(False)
            self._refresh_aspect_hint()
            return

        count = len(self.reference_images)
        self.file_count_label.setText(f"✓ {count} reference image{'s' if count != 1 else ''} ready")
        if hasattr(self, 'hero_hint'):
            self.hero_hint.setText(f"{count} reference image{'s' if count != 1 else ''} locked in.")
        if hasattr(self, 'clear_refs_button'):
            self.clear_refs_button.setEnabled(True)

        cols = max(1, min(4, len(self.reference_images)))
        for index, path in enumerate(self.reference_images):
            card = QtWidgets.QFrame()
            card.setObjectName("thumbCard")
            card_layout = QtWidgets.QVBoxLayout(card)
            card_layout.setContentsMargins(8, 8, 8, 8)
            card_layout.setSpacing(6)

            preview = QtWidgets.QLabel()
            preview.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            preview.setMinimumSize(96, 96)
            pixmap = create_pixmap(path, QtCore.QSize(120, 120))
            if pixmap:
                preview.setPixmap(pixmap)
            else:
                preview.setText("No preview")
            card_layout.addWidget(preview)

            name_label = QtWidgets.QLabel(Path(path).name)
            name_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            name_label.setProperty("class", "sectionHint")
            card_layout.addWidget(name_label)

            remove_btn = QtWidgets.QPushButton("Remove")
            remove_btn.setProperty("class", "outline")
            remove_btn.clicked.connect(lambda _, p=path: self._remove_reference_by_path(p))
            card_layout.addWidget(remove_btn)

            row = index // cols
            col = index % cols
            self.gallery_grid.addWidget(card, row, col)

        primary_path = self.reference_images[0]
        preview_pix = create_pixmap(primary_path, QtCore.QSize(640, 360))
        if preview_pix:
            self.reference_preview.setPixmap(preview_pix)
            self.reference_preview.setText("")
        else:
            self.reference_preview.setPixmap(QtGui.QPixmap())
            self.reference_preview.setText("Could not preview image")

        self._refresh_aspect_hint()

    def _check_reference_sizes(self) -> bool:
        if not self.reference_images:
            return True

        try:
            from api_clients.fal_client import FalClient

            client = FalClient()
            data = client.check_reference_sizes(self.reference_images, "normal")
            total_mb = data.get("total_size_mb", 0.0)

            if total_mb <= 3.5:
                return True

            dialog = SizeLimitDialog(self, total_mb, len(self.reference_images))
            choice = dialog.exec_option()

            if choice in {"normal", "high", "maximum"}:
                # Processor will compress during run; no change needed here
                return True
            if choice == "cancel":
                QtWidgets.QMessageBox.information(
                    self,
                    "Select different images",
                    "Please choose smaller references or reduce the number of images.",
                )
                self.reference_images.clear()
                self._update_reference_display()
                return False
            # keep existing selection even though it may fail
            return True
        except Exception as exc:  # noqa: BLE001
            QtWidgets.QMessageBox.warning(
                self,
                "Reference size",
                f"Could not validate reference sizes: {exc}",
            )
            return True

    # ------------------------------------------------------------------
    # Workflow actions
    # ------------------------------------------------------------------
    def start_processing(self) -> None:
        if self._processing:
            QtWidgets.QMessageBox.warning(self, "Processing", "A run is already in progress.")
            return

        if not self._validate_settings():
            return

        self._processing = True
        self._cancel_requested = False
        self.start_button.setEnabled(False)
        self.start_button.setText("Processing...")
        self.cancel_button.setEnabled(True)
        self.back_button.setEnabled(False)

        self.gemini_prompt = self.prompt_edit.toPlainText().strip() or Config.DEFAULT_GEMINI_PROMPT
        self.workflow_stack.setCurrentWidget(self.processing_page)
        self._reset_processing_view()
        self.processing_title.setText("Preparing run...")
        self.status_label.setText("Preparing run...")

        input_folder = self.input_path_edit.text().strip()
        output_folder = self.output_path_edit.text().strip()
        size = self.size_combo.currentData() or self.size_combo.currentText()
        self.image_size = size
        num_images = self.count_spin.value()
        seed_text = self.seed_edit.text().strip()
        try:
            seed_value = int(seed_text)
            seed = None if seed_value == -1 else seed_value
        except ValueError:
            seed = None

        gemini_key = self.gemini_edit.text().strip() or self.gemini_key
        fal_key = self.fal_edit.text().strip() or self.fal_key
        references = list(self.reference_images)

        self._worker_thread = QtCore.QThread(self)
        self._worker = ProcessorWorker(
            input_folder=input_folder,
            output_folder=output_folder,
            image_size=size,
            num_images=num_images,
            references=references,
            prompt=self.gemini_prompt,
            seed=seed,
            gemini_key=gemini_key,
            fal_key=fal_key,
        )
        self._worker.moveToThread(self._worker_thread)
        self._worker_thread.started.connect(self._worker.run)
        self._worker.progress.connect(self._on_progress_update)
        self._worker.stage.connect(self._handle_stage_event)
        self._worker.result_ready.connect(self._handle_results)
        self._worker.error.connect(self._on_processing_error)
        self._worker.finished.connect(self._on_worker_finished)
        self._worker.finished.connect(self._worker_thread.quit)
        self._worker_thread.finished.connect(self._worker_thread.deleteLater)
        self._worker_thread.start()

    def cancel_processing(self) -> None:
        if not self._processing or self._cancel_requested:
            return
        self._cancel_requested = True
        self.status_label.setText("Cancelling after current image...")
        self.processing_title.setText("Cancelling run")
        self.cancel_button.setEnabled(False)

        if self._worker:
            self._worker.request_cancel()

    def show_setup_view(self) -> None:
        if self._processing:
            QtWidgets.QMessageBox.information(
                self,
                "Processing",
                "Please wait for the current run to finish before returning to setup.",
            )
            return
        self.workflow_stack.setCurrentWidget(self.setup_page)

    # ------------------------------------------------------------------
    # Worker callbacks
    # ------------------------------------------------------------------
    def _on_progress_update(self, current: int, total: int, percent: float, message: str) -> None:
        total = max(total, 1)
        self.progress_bar.setRange(0, total)
        self.progress_bar.setValue(min(current, total))
        label = message or "Processing queue"
        self.status_label.setText(label)
        completed = max(current - 1, 0)
        self.stats_label.setText(
            f"Queued: {total}\nRunning: {current}\nCompleted: {completed}"
        )

    def _apply_seedream_status(self, base_text: str, payload: Dict) -> None:
        target_size = payload.get("target_size") or self.current_target_size
        aspect = payload.get("aspect_info") or self.current_aspect_info or {}
        if target_size:
            self.current_target_size = target_size
        if aspect:
            self.current_aspect_info = aspect

        detail = None
        if target_size:
            detail = target_size
            ratio_label = aspect.get("ratio_label") or aspect.get("closest_ratio_label")
            if aspect.get("manual_override"):
                ratio_label = None
            if aspect.get("fallback") and not aspect.get("manual_override"):
                ratio_label = aspect.get("closest_ratio_label")
            if ratio_label and ratio_label not in detail:
                detail = f"{detail} • {ratio_label}"
        if detail:
            self.seedream_status.setText(f"{base_text} ({detail})")
        else:
            self.seedream_status.setText(base_text)

    def _handle_stage_event(self, event: str, payload: Dict) -> None:
        if event == "start_image":
            index = payload.get("index", 1)
            total = payload.get("total", 1)
            image_path = payload.get("image_path")
            name = Path(image_path).name if image_path else "Unknown"
            self.processing_title.setText(f"Processing image {index} of {total}")
            self.current_image_label.setToolTip(name)
            pixmap = create_pixmap(image_path, QtCore.QSize(360, 360))
            if pixmap:
                self.current_image_label.setPixmap(pixmap)
                self.current_image_label.setText("")
            else:
                self.current_image_label.setPixmap(QtGui.QPixmap())
                self.current_image_label.setText(name)
            self.caption_status.setText("Gemini analysing...")
            self.caption_view.setPlainText("Gemini is crafting a caption...")
            self._apply_seedream_status("Seedream pending", payload)
            self.output_image_label.setPixmap(QtGui.QPixmap())
            self.output_image_label.setToolTip("")
            self.output_image_label.setText("Seedream pending")
        elif event == "caption_ready":
            prompt = payload.get("prompt", "")
            self.caption_view.setPlainText(prompt)
            self.caption_status.setText("Gemini caption ready")
        elif event == "generation_started":
            self._apply_seedream_status("Seedream generating...", payload)
            self.output_image_label.setText("Seedream is generating...")
        elif event == "generation_ready":
            paths = payload.get("generated_paths", [])
            primary = paths[0] if paths else None
            if primary:
                self.output_image_label.setToolTip(Path(primary).name)
                pixmap = create_pixmap(primary, QtCore.QSize(360, 360))
                if pixmap:
                    self.output_image_label.setPixmap(pixmap)
                    self.output_image_label.setText("")
                else:
                    self.output_image_label.setPixmap(QtGui.QPixmap())
                    self.output_image_label.setText("Result image unavailable")
                self._apply_seedream_status("Seedream result ready", payload)
            else:
                self.output_image_label.setPixmap(QtGui.QPixmap())
                self.output_image_label.setToolTip("")
                self.output_image_label.setText("Seedream output missing")
                self._apply_seedream_status("Seedream failed to produce an image", payload)
        elif event == "image_complete":
            result = payload.get("result", payload)
            self._record_result(result)
        elif event == "cancelled":
            self.status_label.setText("Cancelling...")
        elif event == "run_cancelled":
            self.processing_title.setText("Run cancelled")
            self.status_label.setText("Processing cancelled")
        elif event == "error":
            message = payload.get("error", "An error occurred")
            self.status_label.setText(f"Error: {message}")
            self._apply_seedream_status("Error during generation", payload)

    def _handle_results(self, results: Dict) -> None:
        if results.get("cancelled"):
            self._on_processing_cancelled(results)
        else:
            self._on_processing_success(results)

    def _on_processing_success(self, results: Dict) -> None:
        self.processing_title.setText("Run complete")
        self.status_label.setText("Processing complete!")
        self.stats_label.setText(
            f"Processed: {results.get('total_processed', 0)}\n"
            f"Success: {results.get('successful', 0)} | Failed: {results.get('failed', 0)}"
        )
        QtWidgets.QMessageBox.information(
            self,
            "Success",
            "Processing complete. Check the timeline for results.",
        )

    def _on_processing_cancelled(self, results: Dict) -> None:
        self.processing_title.setText("Run cancelled")
        self.status_label.setText("Processing cancelled")
        self.stats_label.setText(
            f"Processed before cancel: {results.get('total_processed', 0)}\n"
            f"Success: {results.get('successful', 0)} | Failed: {results.get('failed', 0)}"
        )

    def _on_processing_error(self, message: str) -> None:
        self.processing_title.setText("Run interrupted")
        self.status_label.setText(f"Error: {message}")
        QtWidgets.QMessageBox.critical(self, "Processing failed", message)

    def _on_worker_finished(self) -> None:
        self._processing = False
        self.start_button.setEnabled(True)
        self.start_button.setText("Start processing")
        self.cancel_button.setEnabled(False)
        self.back_button.setEnabled(True)
        self._worker = None
        self._worker_thread = None

    # ------------------------------------------------------------------
    # Results timeline
    # ------------------------------------------------------------------
    def _record_result(self, result: Dict) -> None:
        self.history.insert(0, result)
        self._refresh_history_list()
        self.history_list.setCurrentRow(0)

    def _refresh_history_list(self) -> None:
        self.history_list.blockSignals(True)
        self.history_list.clear()
        for entry in self.history[:30]:
            name = Path(entry.get("original", "")).name or "Unknown"
            status = entry.get("status", "unknown").capitalize()
            timestamp = entry.get("timestamp") or datetime.now().strftime("%H:%M:%S")
            target_size = entry.get("target_size")
            if target_size:
                item_text = f"{timestamp} • {name} • {status} • {target_size}"
            else:
                item_text = f"{timestamp} • {name} • {status}"
            item = QtWidgets.QListWidgetItem(item_text)
            status_key = entry.get("status")
            if status_key == "success":
                item.setForeground(QtGui.QColor("#bbf7d0"))
            elif status_key == "failed":
                item.setForeground(QtGui.QColor("#fde68a"))
            elif status_key == "error":
                item.setForeground(QtGui.QColor("#fecaca"))
            self.history_list.addItem(item)
        self.history_list.blockSignals(False)

    def _on_history_selection(self) -> None:
        row = self.history_list.currentRow()
        if row < 0 or row >= len(self.history):
            self.results_preview.setPixmap(QtGui.QPixmap())
            self.results_preview.setText("Results will appear here")
            self.prompt_view.clear()
            self.results_meta.setText("")
            self.open_image_button.setEnabled(False)
            self.copy_prompt_button.setEnabled(False)
            return

        entry = self.history[row]
        prompt = entry.get("full_prompt") or entry.get("prompt") or ""
        generated = entry.get("generated_images", [])
        primary = generated[0] if generated else None
        name = Path(entry.get("original", "")).name or "Unknown"
        status = entry.get("status", "unknown")
        status_label = status.capitalize()
        timestamp = entry.get("timestamp") or datetime.now().strftime("%H:%M:%S")
        target_size = entry.get("target_size")
        aspect = entry.get("aspect_info") or {}
        ratio_label = aspect.get("ratio_label") or aspect.get("closest_ratio_label")
        if aspect.get("manual_override"):
            ratio_label = None
        detail_parts = [name, status_label, timestamp]
        if target_size:
            detail_parts.append(str(target_size))
        if ratio_label and (not target_size or ratio_label not in str(target_size)):
            detail_parts.append(ratio_label)
        self.results_meta.setText(" • ".join(detail_parts))
        self.prompt_view.setPlainText(prompt)

        pixmap = create_pixmap(primary, QtCore.QSize(480, 480)) if primary else None
        if pixmap:
            self.results_preview.setPixmap(pixmap)
            self.results_preview.setText("")
        else:
            self.results_preview.setPixmap(QtGui.QPixmap())
            self.results_preview.setText("No image available")
        self.results_preview.setToolTip(Path(primary).name if primary else "")

        self.open_image_button.setEnabled(bool(primary))
        self.copy_prompt_button.setEnabled(bool(prompt))

    def _open_selected_image(self) -> None:
        row = self.history_list.currentRow()
        if row < 0 or row >= len(self.history):
            return
        entry = self.history[row]
        generated = entry.get("generated_images", [])
        primary = generated[0] if generated else None
        prompt = entry.get("full_prompt") or entry.get("prompt") or ""
        if not primary or not os.path.exists(primary):
            QtWidgets.QMessageBox.information(self, "Preview", "Image file is not available.")
            return

        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Image preview")
        dialog.resize(820, 720)
        layout = QtWidgets.QVBoxLayout(dialog)
        layout.setContentsMargins(18, 18, 18, 18)
        layout.setSpacing(12)

        pixmap = create_pixmap(primary, QtCore.QSize(760, 520))
        label = QtWidgets.QLabel()
        label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        if pixmap:
            label.setPixmap(pixmap)
        layout.addWidget(label)

        if prompt:
            prompt_view = QtWidgets.QPlainTextEdit(prompt)
            prompt_view.setReadOnly(True)
            layout.addWidget(prompt_view)

        buttons = QtWidgets.QDialogButtonBox()
        copy_button = buttons.addButton("Copy image path", QtWidgets.QDialogButtonBox.ButtonRole.ActionRole)
        copy_button.clicked.connect(lambda: self.copy_to_clipboard(primary))
        buttons.addButton(QtWidgets.QDialogButtonBox.StandardButton.Close)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        dialog.exec()

    def _copy_selected_prompt(self) -> None:
        row = self.history_list.currentRow()
        if row < 0 or row >= len(self.history):
            return
        entry = self.history[row]
        prompt = entry.get("full_prompt") or entry.get("prompt")
        if prompt:
            self.copy_to_clipboard(prompt)

    # ------------------------------------------------------------------
    # Settings + persistence
    # ------------------------------------------------------------------
    def _validate_settings(self) -> bool:
        input_folder = self.input_path_edit.text().strip()
        output_folder = self.output_path_edit.text().strip()

        if not input_folder:
            QtWidgets.QMessageBox.warning(self, "Settings", "Select an input folder.")
            return False
        if not output_folder:
            QtWidgets.QMessageBox.warning(self, "Settings", "Select an output folder.")
            return False
        if not self.gemini_edit.text().strip() and not self.gemini_key:
            QtWidgets.QMessageBox.warning(self, "Settings", "Enter a Gemini API key in Settings.")
            return False
        if not self.fal_edit.text().strip() and not self.fal_key:
            QtWidgets.QMessageBox.warning(self, "Settings", "Enter a Fal.ai API key in Settings.")
            return False
        return True

    def save_settings(self) -> None:
        self.gemini_prompt = self.prompt_edit.toPlainText().strip()
        current_size = self.size_combo.currentData()
        self.image_size = current_size if current_size else self.size_combo.currentText()
        self.num_images = str(self.count_spin.value())
        self.seed_value = self.seed_edit.text().strip() or "-1"
        self.input_folder = self.input_path_edit.text().strip()
        self.output_folder = self.output_path_edit.text().strip()
        self.gemini_key = self.gemini_edit.text().strip()
        self.fal_key = self.fal_edit.text().strip()

        env_content = (
            "# API Keys\n"
            f"GEMINI_API_KEY={self.gemini_key}\n"
            f"FAL_API_KEY={self.fal_key}\n\n"
            "# Default Settings\n"
            f"DEFAULT_IMAGE_SIZE={self.image_size}\n"
            f"DEFAULT_IMAGE_COUNT={self.num_images}\n"
            f"DEFAULT_INPUT_FOLDER={self.input_folder}\n"
            f"DEFAULT_OUTPUT_FOLDER={self.output_folder}\n\n"
            "# Gemini Settings\n"
            "GEMINI_MODEL=gemini-2.5-flash-preview-09-2025\n\n"
            "# Seedream Settings\n"
            "SEEDREAM_TEXT_MODEL=fal-ai/bytedance/seedream/v4/text-to-image\n"
            "SEEDREAM_EDIT_MODEL=fal-ai/bytedance/seedream/v4/edit\n"
        )

        try:
            with open(".env", "w", encoding="utf-8") as env_file:
                env_file.write(env_content)
            with open("custom_prompt.txt", "w", encoding="utf-8") as prompt_file:
                prompt_file.write(self.gemini_prompt)
            Config.update_api_keys(self.gemini_key, self.fal_key)
            QtWidgets.QMessageBox.information(self, "Settings", "Settings saved successfully.")
        except Exception as exc:  # noqa: BLE001
            QtWidgets.QMessageBox.critical(self, "Settings", f"Failed to save settings: {exc}")

    def load_settings(self) -> None:
        try:
            if os.path.exists("custom_prompt.txt"):
                with open("custom_prompt.txt", "r", encoding="utf-8") as prompt_file:
                    self.gemini_prompt = prompt_file.read()
                    self.prompt_edit.setPlainText(self.gemini_prompt)

            if os.path.exists(".env"):
                from dotenv import load_dotenv

                load_dotenv(override=True)
                self.gemini_key = os.getenv("GEMINI_API_KEY", "")
                self.fal_key = os.getenv("FAL_API_KEY", "")
                self.gemini_edit.setText(self.gemini_key)
                self.fal_edit.setText(self.fal_key)
        except Exception as exc:  # noqa: BLE001
            print(f"Error loading settings: {exc}")

        self._refresh_aspect_hint()

    def reset_settings(self) -> None:
        if QtWidgets.QMessageBox.question(
            self,
            "Reset settings",
            "Reset all fields back to defaults?",
        ) != QtWidgets.QMessageBox.StandardButton.Yes:
            return

        index = self.size_combo.findData("auto_4K")
        if index != -1:
            self.size_combo.setCurrentIndex(index)
        else:
            self.size_combo.setCurrentIndex(0)
        self.image_size = 'auto_4K'
        self.count_spin.setValue(1)
        self.seed_edit.setText("-1")
        self.prompt_edit.setPlainText(Config.DEFAULT_GEMINI_PROMPT)
        self._refresh_aspect_hint()
        QtWidgets.QMessageBox.information(self, "Reset", "Settings restored to defaults.")

    def test_connections(self) -> None:
        try:
            Config.update_api_keys(self.gemini_edit.text().strip(), self.fal_edit.text().strip())
            processor = ImageGenerationProcessor(
                gemini_key=self.gemini_edit.text().strip(),
                fal_key=self.fal_edit.text().strip(),
            )
            results = processor.validate_apis()
            messages = [
                "✅ Gemini API: Connected" if results.get("gemini") else "❌ Gemini API: Failed",
                "✅ Fal.ai API: Connected" if results.get("fal") else "❌ Fal.ai API: Failed",
            ]
            QtWidgets.QMessageBox.information(self, "API status", "\n".join(messages))
        except Exception as exc:  # noqa: BLE001
            QtWidgets.QMessageBox.critical(self, "API status", f"Failed to test APIs: {exc}")

    # ------------------------------------------------------------------
    # Utilities
    # ------------------------------------------------------------------
    def copy_to_clipboard(self, text: str) -> None:
        QtWidgets.QApplication.clipboard().setText(text)
        QtWidgets.QMessageBox.information(self, "Clipboard", "Copied to clipboard.")

    def _select_input_folder(self) -> None:
        folder = QtWidgets.QFileDialog.getExistingDirectory(self, "Select input folder", self.input_path_edit.text())
        if folder:
            self.input_path_edit.setText(folder)
            self._refresh_aspect_hint()

    def _select_output_folder(self) -> None:
        folder = QtWidgets.QFileDialog.getExistingDirectory(self, "Select output folder", self.output_path_edit.text())
        if folder:
            self.output_path_edit.setText(folder)

    def _reset_processing_view(self) -> None:
        self.progress_bar.setRange(0, 1)
        self.progress_bar.setValue(0)
        self.status_label.setText("Run idle")
        self.stats_label.setText("Processed: 0\nSuccess: 0 | Failed: 0")
        self.current_image_label.setText("Waiting to start")
        self.current_image_label.setPixmap(QtGui.QPixmap())
        self.output_image_label.setText("Seedream pending")
        self.output_image_label.setPixmap(QtGui.QPixmap())
        self.caption_view.clear()
        self.caption_status.setText("Gemini pending")
        self.seedream_status.setText("Seedream pending")
        self.history.clear()
        self.history_list.clear()
        self.results_preview.setPixmap(QtGui.QPixmap())
        self.results_preview.setText("Results will appear here")
        self.results_meta.setText("")
        self.prompt_view.clear()
        self.open_image_button.setEnabled(False)
        self.copy_prompt_button.setEnabled(False)
        self.current_target_size = None
        self.current_aspect_info = None

    def closeEvent(self, event: QtGui.QCloseEvent) -> None:  # noqa: N802 - Qt naming
        if self._processing:
            if QtWidgets.QMessageBox.question(
                self,
                "Exit",
                "A run is still in progress. Stop processing and exit?",
            ) == QtWidgets.QMessageBox.StandardButton.Yes:
                if self._worker:
                    self._worker.request_cancel()
                if self._worker_thread:
                    self._worker_thread.quit()
                    self._worker_thread.wait(2000)
            else:
                event.ignore()
                return
        if self._log_callback:
            try:
                gui_handler.remove_callback(self._log_callback)
            except Exception:
                pass
        super().closeEvent(event)


def main() -> None:
    app = QtWidgets.QApplication(sys.argv)
    window = SeedreamWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
